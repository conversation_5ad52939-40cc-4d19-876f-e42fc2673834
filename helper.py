import json
import logging
import re
import time
from typing import Any, Dict, Optional, Union

import dirtyjson
import json5
import requests
from requests.adapters import HTT<PERSON><PERSON>pter
from urllib3.util.retry import Retry

# Configure logging
logger = logging.getLogger(__name__)

# Constants
DEFAULT_TIMEOUT = 30
MAX_RETRIES = 3
BACKOFF_FACTOR = 0.3


def create_robust_session() -> requests.Session:
    """Create a requests session with retry strategy and timeout handling."""
    session = requests.Session()

    # Define retry strategy
    retry_strategy = Retry(
        total=MAX_RETRIES,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS"],
        backoff_factor=BACKOFF_FACTOR,
    )

    # Mount adapter with retry strategy
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session


def normalize_name(name: Optional[str]) -> str:
    """
    Normalize a name by replacing spaces and hyphens with underscores.

    Args:
        name: The name to normalize. Can be None.

    Returns:
        Normalized name string. Returns empty string if input is None.

    Raises:
        TypeError: If name is not a string or None.
    """
    if name is None:
        logger.warning("normalize_name called with None input")
        return ""

    if not isinstance(name, str):
        raise TypeError(f"Expected string or None, got {type(name).__name__}")

    try:
        # Replace spaces and hyphens with underscores
        name = re.sub(r"[\s\-]+", "_", name)
        # Replace multiple underscores with single underscore
        name = re.sub(r"_+", "_", name)
        # Strip leading and trailing underscores
        name = name.strip("_")
        return name
    except Exception as e:
        logger.error(f"Error normalizing name '{name}': {e}")
        return ""


def context_component(data: Optional[Dict[str, Any]]) -> str:
    """
    Fetch component context from the API and format it for display.

    Args:
        data: Dictionary containing 'category' and 'name' keys

    Returns:
        Formatted context string or error message
    """
    # Input validation
    if data is None:
        logger.error("context_component called with None data")
        return "Error: No data provided"

    if not isinstance(data, dict):
        logger.error(f"context_component called with invalid data type: {type(data)}")
        return f"Error: Expected dictionary, got {type(data).__name__}"

    category = data.get("category")
    name_key = data.get("name")

    if not category:
        logger.warning("Missing category in component data")
        return "Error: Category is required"

    if not name_key:
        logger.warning("Missing name in component data")
        return "Error: Component name is required"

    url = "https://app-dev.rapidinnovation.dev/api/v1/components?refresh=true"
    session = create_robust_session()

    try:
        logger.info(f"Fetching components from API: {url}")
        response = session.get(url, timeout=DEFAULT_TIMEOUT)
        response.raise_for_status()

        try:
            components = response.json()
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            return "Error: Invalid JSON response from components API"

    except requests.exceptions.Timeout:
        logger.error(f"Timeout while fetching components from {url}")
        return "Error: Request timeout while fetching components"
    except requests.exceptions.ConnectionError:
        logger.error(f"Connection error while fetching components from {url}")
        return "Error: Failed to connect to components API"
    except requests.exceptions.HTTPError as e:
        logger.error(f"HTTP error while fetching components: {e}")
        return f"Error: HTTP {e.response.status_code} - Failed to fetch components"
    except Exception as e:
        logger.error(f"Unexpected error while fetching components: {e}")
        return f"Error: Failed to fetch components - {str(e)}"

    # Validate components data structure
    if not isinstance(components, dict):
        logger.error(f"Components API returned invalid data type: {type(components)}")
        return "Error: Invalid components data structure"

    if category not in components:
        available_categories = list(components.keys()) if components else []
        logger.warning(
            f"Category '{category}' not found. Available: {available_categories}"
        )
        return f"Error: Category '{category}' not found. Available categories: {', '.join(available_categories)}"

    category_data = components[category]
    if not isinstance(category_data, dict):
        logger.error(f"Category data is not a dictionary: {type(category_data)}")
        return f"Error: Invalid data structure for category '{category}'"

    if name_key not in category_data:
        available_components = list(category_data.keys()) if category_data else []
        logger.warning(
            f"Component '{name_key}' not found in category '{category}'. Available: {available_components}"
        )
        return f"Error: Component '{name_key}' not found in category '{category}'. Available components: {', '.join(available_components)}"

    component = category_data[name_key]
    if not isinstance(component, dict):
        logger.error(f"Component data is not a dictionary: {type(component)}")
        return f"Error: Invalid component data structure for '{name_key}'"

    # Extract component data with safe defaults
    name = component.get("name", "N/A")
    description = component.get("description", "No description")
    inputs = component.get("inputs", [])
    outputs = component.get("outputs", [])

    # Validate inputs and outputs are lists
    if not isinstance(inputs, list):
        logger.warning(f"Component inputs is not a list: {type(inputs)}")
        inputs = []
    if not isinstance(outputs, list):
        logger.warning(f"Component outputs is not a list: {type(outputs)}")
        outputs = []

    try:
        # Build context string safely
        context = f"Name : {name}\nDescription : {description}\nOriginalType : {name}\nType : Component\n"

        context += "Inputs :-\n"
        for i in inputs:
            if not isinstance(i, dict):
                logger.warning(f"Skipping invalid input item: {type(i)}")
                continue

            context += f"Input Name : {i.get('name', 'N/A')}\n"
            context += f"Input Info : {i.get('info', '')}\n"
            context += f"Input Type : {i.get('input_type', '')}\n"

            input_types = i.get("input_types")
            if input_types and isinstance(input_types, list):
                try:
                    context += (
                        "Input Types : " + ", ".join(str(t) for t in input_types) + "\n"
                    )
                except Exception as e:
                    logger.warning(f"Error processing input_types: {e}")

            if i.get("required"):
                context += "Required\n"
            if i.get("is_handle"):
                context += "Handle\n"
            if i.get("is_list"):
                context += "List\n"
            if i.get("real_time_refresh"):
                context += "Real Time Refresh\n"
            if i.get("advanced"):
                context += "Advanced\n"
            if i.get("value") is not None:
                context += f"Default Value : {i['value']}\n"

            options = i.get("options")
            if options and isinstance(options, list):
                try:
                    context += (
                        "Options : " + ", ".join(str(opt) for opt in options) + "\n"
                    )
                except Exception as e:
                    logger.warning(f"Error processing options: {e}")
            context += "\n"

        context += "Outputs :-\n"
        for o in outputs:
            if not isinstance(o, dict):
                logger.warning(f"Skipping invalid output item: {type(o)}")
                continue

            context += f"Output Name : {o.get('name', 'N/A')}\n"
            context += f"Output Type : {o.get('output_type', '')}\n"
            if o.get("semantic_type"):
                context += f"Semantic Type : {o['semantic_type']}\n"
            if o.get("method"):
                context += f"Method : {o['method']}\n"
            context += "\n"

        logger.info(
            f"Successfully generated context for component '{name_key}' in category '{category}'"
        )
        return context

    except Exception as e:
        logger.error(f"Error building context string for component '{name_key}': {e}")
        return f"Error: Failed to build component context - {str(e)}"


def workflow_context(data: Optional[Dict[str, Any]]) -> str:
    """
    Fetch workflow context from the API and format it for display.

    Args:
        data: Dictionary containing 'id' key for workflow ID

    Returns:
        Formatted context string or error message
    """
    # Input validation
    if data is None:
        logger.error("workflow_context called with None data")
        return "Error: No data provided"

    if not isinstance(data, dict):
        logger.error(f"workflow_context called with invalid data type: {type(data)}")
        return f"Error: Expected dictionary, got {type(data).__name__}"

    workflow_id = data.get("id")
    if not workflow_id:
        logger.warning("Missing workflow ID in data")
        return "Error: Workflow ID is required"

    if not isinstance(workflow_id, str):
        logger.warning(f"Workflow ID is not a string: {type(workflow_id)}")
        return f"Error: Workflow ID must be a string, got {type(workflow_id).__name__}"

    url = f"https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows/{workflow_id}"
    session = create_robust_session()

    try:
        logger.info(f"Fetching workflow from API: {url}")
        response = session.get(url, timeout=DEFAULT_TIMEOUT)
        response.raise_for_status()

        try:
            workflow_data = response.json()
        except json.JSONDecodeError as e:
            logger.error(
                f"Failed to parse JSON response for workflow {workflow_id}: {e}"
            )
            return "Error: Invalid JSON response from workflow API"

    except requests.exceptions.Timeout:
        logger.error(f"Timeout while fetching workflow {workflow_id}")
        return f"Error: Request timeout while fetching workflow {workflow_id}"
    except requests.exceptions.ConnectionError:
        logger.error(f"Connection error while fetching workflow {workflow_id}")
        return "Error: Failed to connect to workflow API"
    except requests.exceptions.HTTPError as e:
        logger.error(f"HTTP error while fetching workflow {workflow_id}: {e}")
        if e.response.status_code == 404:
            return f"Error: Workflow '{workflow_id}' not found"
        return f"Error: HTTP {e.response.status_code} - Failed to fetch workflow"
    except Exception as e:
        logger.error(f"Unexpected error while fetching workflow {workflow_id}: {e}")
        return f"Error: Failed to fetch workflow - {str(e)}"

    # Validate workflow data structure
    if not isinstance(workflow_data, dict):
        logger.error(f"Workflow API returned invalid data type: {type(workflow_data)}")
        return "Error: Invalid workflow data structure"

    workflow = workflow_data.get("workflow")
    if not workflow:
        logger.warning(f"No workflow found in response for ID '{workflow_id}'")
        return f"Error: No workflow found for ID '{workflow_id}'"

    if not isinstance(workflow, dict):
        logger.error(f"Workflow data is not a dictionary: {type(workflow)}")
        return f"Error: Invalid workflow data structure for '{workflow_id}'"

    # Extract workflow data with safe defaults
    name = workflow.get("name", "N/A")
    description = workflow.get("description", "No description")
    inputs = workflow.get("start_nodes", [])

    # Validate inputs is a list
    if not isinstance(inputs, list):
        logger.warning(f"Workflow start_nodes is not a list: {type(inputs)}")
        inputs = []

    # Define standard workflow outputs
    outputs = [
        {
            "name": "execution_status",
            "display_name": "Execution Status",
            "output_type": "string",
        },
        {
            "name": "workflow_execution_id",
            "display_name": "Execution ID",
            "output_type": "string",
        },
        {"name": "message", "display_name": "Message", "output_type": "string"},
    ]

    try:
        # Build context string safely
        context = (
            f"Name : {name}\n"
            f"Description : {description}\n"
            f"OriginalType : workflow-{workflow_id}\n"
            f"Type : Workflow\n"
        )

        context += "Inputs :-\n"
        for i in inputs:
            if not isinstance(i, dict):
                logger.warning(
                    f"Skipping invalid input item in workflow {workflow_id}: {type(i)}"
                )
                continue

            field_name = i.get("field", "N/A")
            field_type = i.get("type", "N/A")
            context += f"Input Name : {field_name}\n"
            context += f"Input Info : {field_name}\n"
            context += f"Input Type : {field_type}\n"
            context += "Required\nHandle\n\n"

        context += "Outputs :-\n"
        for o in outputs:
            if not isinstance(o, dict):
                logger.warning(
                    f"Skipping invalid output item in workflow {workflow_id}: {type(o)}"
                )
                continue

            context += f"Output Name : {o.get('name', 'N/A')}\n"
            context += f"Output Type : {o.get('output_type', 'N/A')}\n\n"

        logger.info(f"Successfully generated context for workflow '{workflow_id}'")
        return context

    except Exception as e:
        logger.error(f"Error building context string for workflow '{workflow_id}': {e}")
        return f"Error: Failed to build workflow context - {str(e)}"


def mcp_context(data: Optional[Dict[str, Any]]) -> str:
    """
    Fetch MCP context from the API and format it for display.

    Args:
        data: Dictionary containing 'id' and 'name' keys for MCP ID and tool name

    Returns:
        Formatted context string or error message
    """
    # Input validation
    if data is None:
        logger.error("mcp_context called with None data")
        return "Error: No data provided"

    if not isinstance(data, dict):
        logger.error(f"mcp_context called with invalid data type: {type(data)}")
        return f"Error: Expected dictionary, got {type(data).__name__}"

    mcp_id = data.get("id")
    tool_name = data.get("name")

    if not mcp_id:
        logger.warning("Missing MCP ID in data")
        return "Error: MCP ID is required"

    if not tool_name:
        logger.warning("Missing tool name in data")
        return "Error: Tool name is required"

    if not isinstance(mcp_id, str):
        logger.warning(f"MCP ID is not a string: {type(mcp_id)}")
        return f"Error: MCP ID must be a string, got {type(mcp_id).__name__}"

    if not isinstance(tool_name, str):
        logger.warning(f"Tool name is not a string: {type(tool_name)}")
        return f"Error: Tool name must be a string, got {type(tool_name).__name__}"

    url = f"https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/{mcp_id}"
    session = create_robust_session()

    try:
        logger.info(f"Fetching MCP from API: {url}")
        response = session.get(url, timeout=DEFAULT_TIMEOUT)
        response.raise_for_status()

        try:
            mcp_data = response.json()
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response for MCP {mcp_id}: {e}")
            return "Error: Invalid JSON response from MCP API"

    except requests.exceptions.Timeout:
        logger.error(f"Timeout while fetching MCP {mcp_id}")
        return f"Error: Request timeout while fetching MCP {mcp_id}"
    except requests.exceptions.ConnectionError:
        logger.error(f"Connection error while fetching MCP {mcp_id}")
        return "Error: Failed to connect to MCP API"
    except requests.exceptions.HTTPError as e:
        logger.error(f"HTTP error while fetching MCP {mcp_id}: {e}")
        if e.response.status_code == 404:
            return f"Error: MCP '{mcp_id}' not found"
        return f"Error: HTTP {e.response.status_code} - Failed to fetch MCP"
    except Exception as e:
        logger.error(f"Unexpected error while fetching MCP {mcp_id}: {e}")
        return f"Error: Failed to fetch MCP - {str(e)}"

    # Validate MCP data structure
    if not isinstance(mcp_data, dict):
        logger.error(f"MCP API returned invalid data type: {type(mcp_data)}")
        return "Error: Invalid MCP data structure"

    mcp = mcp_data.get("mcp", {})
    if not isinstance(mcp, dict):
        logger.error(f"MCP data is not a dictionary: {type(mcp)}")
        return f"Error: Invalid MCP data structure for '{mcp_id}'"

    # Extract tools safely
    mcp_tools_config = mcp.get("mcp_tools_config", {})
    if not isinstance(mcp_tools_config, dict):
        logger.warning(
            f"mcp_tools_config is not a dictionary: {type(mcp_tools_config)}"
        )
        mcp_tools_config = {}

    tools = mcp_tools_config.get("tools", [])
    if not isinstance(tools, list):
        logger.warning(f"Tools is not a list: {type(tools)}")
        tools = []

    # Find the specific tool
    tool = None
    for t in tools:
        if isinstance(t, dict) and t.get("name") == tool_name:
            tool = t
            break

    if not tool:
        available_tools = [
            t.get("name", "Unknown") for t in tools if isinstance(t, dict)
        ]
        logger.warning(
            f"Tool '{tool_name}' not found in MCP '{mcp_id}'. Available: {available_tools}"
        )
        return f"Error: Tool '{tool_name}' not found in MCP '{mcp_id}'. Available tools: {', '.join(available_tools)}"

    # Extract data safely
    mcp_name = mcp.get("name", "N/A")
    name = tool.get("name", "N/A")
    description = tool.get("description", "No description")
    input_schema = tool.get("input_schema", {})
    output_schema = tool.get("output_schema", {})

    # Validate schemas are dictionaries
    if not isinstance(input_schema, dict):
        logger.warning(f"Input schema is not a dictionary: {type(input_schema)}")
        input_schema = {}
    if not isinstance(output_schema, dict):
        logger.warning(f"Output schema is not a dictionary: {type(output_schema)}")
        output_schema = {}

    # Generate original_type safely (fix the NameError bug)
    try:
        original_type = "MCP_" + normalize_name(f"{mcp_name} - {name}")
    except Exception as e:
        logger.warning(f"Error generating normalized original_type: {e}")
        # Fallback to simple concatenation
        safe_mcp_name = str(mcp_name).replace(" ", "_").replace("-", "_")
        safe_name = str(name).replace(" ", "_").replace("-", "_")
        original_type = f"MCP_{safe_mcp_name}_{safe_name}"

    try:
        # Build context string safely
        context = (
            f"Name : {name}\n"
            f"Description : {description}\n"
            f"OriginalType : {original_type}\n"
            f"Type : MCP\n"
            f"MCP_id : {mcp_id}\n"
            f"ToolName : {tool_name}\n"
        )

        context += "Inputs :-\n"

        # Extract schema components safely
        required_input = input_schema.get("required", [])
        if not isinstance(required_input, list):
            logger.warning(f"Required input is not a list: {type(required_input)}")
            required_input = []

        defs = input_schema.get("$defs", {})
        if not isinstance(defs, dict):
            logger.warning(f"$defs is not a dictionary: {type(defs)}")
            defs = {}

        properties = input_schema.get("properties", {})
        if not isinstance(properties, dict):
            logger.warning(f"Properties is not a dictionary: {type(properties)}")
            properties = {}

        # Process each property safely
        for i, property_details in properties.items():
            if not isinstance(property_details, dict):
                logger.warning(
                    f"Skipping invalid property '{i}': {type(property_details)}"
                )
                continue

            context += f"Input Name : {i}\n"
            context += f"Input Info : {property_details.get('description', '')}\n"

            property_type = property_details.get("type", "Any")
            options = None
            property_scheme = None
            items = None
            is_array = False

            try:
                if property_type:
                    context += f"Input Type : {property_type}\n"
                    if property_type == "object":
                        property_scheme = property_details.get("properties", {})
                        if not isinstance(property_scheme, dict):
                            property_scheme = {}
                    if property_type == "array":
                        items = property_details.get("items", [])
                        is_array = True

                elif "anyOf" in property_details:
                    any_of = property_details["anyOf"]
                    if isinstance(any_of, list):
                        context += "Input Type : "
                        for j in any_of:
                            if not isinstance(j, dict):
                                continue
                            if "type" in j and j["type"] != "null":
                                context += j["type"] + "\n"
                                if j["type"] == "object":
                                    property_scheme = j.get("properties", {})
                                    if not isinstance(property_scheme, dict):
                                        property_scheme = {}
                                if j["type"] == "array":
                                    items = j.get("items")
                                    is_array = True
                            elif "$ref" in j:
                                try:
                                    ref_key = j["$ref"].split("/")[-1]
                                    ref = defs.get(ref_key, {})
                                    if isinstance(ref, dict):
                                        context += ref.get("type", "N/A") + "\n"
                                        if "enum" in ref and isinstance(
                                            ref["enum"], list
                                        ):
                                            options = ref["enum"]
                                        if ref.get("type") == "object":
                                            property_scheme = ref.get("properties", {})
                                            if not isinstance(property_scheme, dict):
                                                property_scheme = {}
                                        if ref.get("type") == "array":
                                            items = ref.get("items")
                                            is_array = True
                                except Exception as e:
                                    logger.warning(
                                        f"Error processing $ref in anyOf: {e}"
                                    )

                elif "$ref" in property_details:
                    try:
                        ref_key = property_details["$ref"].split("/")[-1]
                        ref = defs.get(ref_key, {})
                        if isinstance(ref, dict):
                            context += f"Input Type : {ref.get('type', 'N/A')}\n"
                            if "enum" in ref and isinstance(ref["enum"], list):
                                options = ref["enum"]
                            if ref.get("type") == "object":
                                property_scheme = ref.get("properties", {})
                                if not isinstance(property_scheme, dict):
                                    property_scheme = {}
                            if ref.get("type") == "array":
                                items = ref.get("items", [])
                                is_array = True
                    except Exception as e:
                        logger.warning(f"Error processing $ref: {e}")

            except Exception as e:
                logger.warning(f"Error processing property '{i}': {e}")
                continue

            # Add property metadata safely
            if i in required_input:
                context += "Required\n"
            context += "Handle\n"
            if is_array:
                context += "List\n"
            if property_details.get("default") is not None:
                context += f"Default Value : {property_details['default']}\n"
            if options:
                try:
                    context += (
                        "Options : " + ", ".join(str(opt) for opt in options) + "\n"
                    )
                except Exception as e:
                    logger.warning(f"Error processing options: {e}")

            # Handle nested properties safely
            if property_scheme and isinstance(property_scheme, dict):
                context += "Properties :-\n"
                for j, prop in property_scheme.items():
                    if not isinstance(prop, dict):
                        continue
                    context += f"> Property Name : {j}\n"
                    context += f"> Property Info : {prop.get('description', '')}\n"
                    if prop.get("type"):
                        context += f"> Property Type : {prop['type']}\n"
                    if prop.get("anyOf") and isinstance(prop["anyOf"], list):
                        context += "> Property Type : "
                        for k in prop["anyOf"]:
                            if (
                                isinstance(k, dict)
                                and "type" in k
                                and k["type"] != "null"
                            ):
                                context += k["type"] + ", "
                        context += "\n"
                    if prop.get("default") is not None:
                        context += f"> Property Default Value : {prop['default']}\n"
                    if prop.get("enum") and isinstance(prop["enum"], list):
                        try:
                            context += (
                                "> Property Options : "
                                + ", ".join(str(e) for e in prop["enum"])
                                + "\n"
                            )
                        except Exception as e:
                            logger.warning(f"Error processing property enum: {e}")
                    context += "> \n"

            if items:
                context += f"Items : {items}\n"
            context += "\n"

        # Process outputs safely
        context += "Outputs :-\n"
        output_properties = output_schema.get("properties", {})
        if isinstance(output_properties, dict):
            for o, details in output_properties.items():
                if not isinstance(details, dict):
                    logger.warning(f"Skipping invalid output '{o}': {type(details)}")
                    continue
                context += f"Output Name : {o}\n"
                context += f"Output Info : {details.get('description', '')}\n"
                context += f"Output Type : {details.get('type', '')}\n\n"

        logger.info(
            f"Successfully generated context for MCP tool '{tool_name}' in MCP '{mcp_id}'"
        )
        return context

    except Exception as e:
        logger.error(
            f"Error building context string for MCP '{mcp_id}', tool '{tool_name}': {e}"
        )
        return f"Error: Failed to build MCP context - {str(e)}"


context_helpers = {
    "component": context_component,
    "workflow": workflow_context,
    "mcp": mcp_context,
}


def safe_loads(text: Optional[str]) -> Any:
    """
    Safely parse JSON text using multiple parsers with fallback options.

    Args:
        text: JSON string to parse. Can be None.

    Returns:
        Parsed JSON object

    Raises:
        ValueError: If all parsing attempts fail
        TypeError: If text is not a string or None
    """
    if text is None:
        logger.warning("safe_loads called with None text")
        raise ValueError("Cannot parse None as JSON")

    if not isinstance(text, str):
        logger.error(f"safe_loads called with invalid type: {type(text)}")
        raise TypeError(f"Expected string or None, got {type(text).__name__}")

    if not text.strip():
        logger.warning("safe_loads called with empty or whitespace-only text")
        raise ValueError("Cannot parse empty string as JSON")

    # First try strict JSON
    try:
        result = json.loads(text)
        logger.debug("Successfully parsed JSON with standard parser")
        return result
    except json.JSONDecodeError as e:
        logger.debug(f"Standard JSON parsing failed: {e}")

    # Next try JSON5 (close to standard, but tolerant)
    try:
        result = json5.loads(text)
        logger.debug("Successfully parsed JSON with JSON5 parser")
        return result
    except Exception as e:
        logger.debug(f"JSON5 parsing failed: {e}")

    # Finally try dirtyjson (super forgiving)
    try:
        result = dirtyjson.loads(text)
        logger.debug("Successfully parsed JSON with dirtyjson parser")
        return result
    except Exception as e:
        logger.error(f"All JSON parsing attempts failed. Last error: {e}")
        # Truncate text for logging if it's too long
        text_preview = text[:200] + "..." if len(text) > 200 else text
        raise ValueError(f"Could not parse JSON: {e}\nRaw text preview: {text_preview}")
 ## TODO Add json_repair