# filename: api_server.py

from typing import List

import uvicorn
from fastapi import FastAP<PERSON>
from pydantic import BaseModel
from sentence_transformers import SentenceTransformer

# Assume you have your embedding model imported and initialized
# from your_embedding_library import embedding_model

app = FastAPI(title="Embedding API")
embedding_model = SentenceTransformer("mixedbread-ai/mxbai-embed-large-v1")


class QueryRequest(BaseModel):
    query: str


class QueryResponse(BaseModel):
    embedding: List[float]


@app.post("/embed", response_model=QueryResponse)
def get_embedding(request: QueryRequest):
    # Generate embedding
    embedding = embedding_model.encode(request.query).tolist()
    return QueryResponse(embedding=embedding)

# if __name__ == "__main__":
#     uvicorn.run("embedding:app", host="*********", port=8000, reload=True)
