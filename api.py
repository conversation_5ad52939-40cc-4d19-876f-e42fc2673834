import json
import uuid
from typing import Any, Dict

from fastapi import FastAPI
from pydantic import BaseModel

from .agent import get_agents
from .graph import get_graph
from .logger_config import get_logger
from .helper import safe_loads

class Payload(BaseModel):
    task: str


app = FastAPI()


def get_workflow(graph, task) -> Dict[str, Any]:
    result = graph(task)
    response: Dict[str, Any] = {}
    result = result.results["post_processing"].result.results["post_processing"]
    post_result = result.result.message["content"][0]["text"]

    if "```json" in post_result:
        post_result = post_result.split("```json")[1].split("```")[0].strip()

    try:
        parsed_result = safe_loads(post_result)
    except:
        return {
            "error": "Failed to parse JSON from post_processing result",
            "raw_result": post_result,
        }

    response = parsed_result
    return response


@app.post("/api/v1/generate_workflow")
async def generate_workflow(payload: Payload):
    task = payload.task
    session_id = str(uuid.uuid4())
    logger = get_logger(session_id)
    agents = get_agents(logger)
    graph = get_graph(agents)
    logger.info(
        json.dumps(
            {
                "message": {"role": "USER_INPUT", "content": task},
                "agent": "prompt_enhancement",
            }
        )
    )

    response = get_workflow(graph, task)
    return response
