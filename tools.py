import os

import requests
from <PERSON>con<PERSON> import <PERSON><PERSON><PERSON>
from strands import tool

from .helper import context_helpers
from .post_processing.components import fulfill_component
from .post_processing.mcp import fulfill_mcp
from .post_processing.workflow import fulfill_workflow

# from helper import context_helpers
# from post_processing.components import fulfill_component
# from post_processing.mcp import fulfill_mcp
# from post_processing.workflow import fulfill_workflow

########
# Loading any asserts or objects
########
# embedding_model = SentenceTransformer("mixedbread-ai/mxbai-embed-large-v1")
PINECONE_API_KEY = os.environ.get("PINECONE_API_KEY")
pc = Pinecone(api_key=PINECONE_API_KEY)
index = pc.Index("tool-embeddings")


def _return_node_template(node_info: dict) -> dict:

    node_type = node_info["type"]
    node_type = node_type.lower().strip()

    if node_type == "component":
        return fulfill_component(
            node_info=node_info,
        )
    elif node_type == "workflow":
        return fulfill_workflow(
            node_info=node_info,
        )
    elif node_type == "mcp":
        return fulfill_mcp(
            node_info=node_info,
        )
    else:
        raise ValueError(f"Unknown node type: {node_type}")


def _return_edge_template(edge):
    template = {
        "animated": True,
        "style": {"strokeWidth": 2, "zIndex": 5},
        "id": "reactflow__edge-start-nodeflow-MCP_Google_Sheets_add_single_row-1753351326708row",
        "source": "start-node",
        "sourceHandle": "flow",
        "target": "MCP_Google_Sheets_add_single_row-1753351326708",
        "targetHandle": "row",
        "type": "default",
        "selected": False,
    }
    template.update(edge)
    template["id"] = (
        f"reactflow__edge{edge['source']}{edge['sourceHandle']}-{edge['target']}{edge['targetHandle']}"
    )
    return template


def _return_edge_template(edge):
    template = {
        "animated": True,
        "style": {"strokeWidth": 2, "zIndex": 5},
        "id": "reactflow__edge-start-nodeflow-MCP_Google_Sheets_add_single_row-1753351326708row",
        "source": "start-node",
        "sourceHandle": "flow",
        "target": "MCP_Google_Sheets_add_single_row-1753351326708",
        "targetHandle": "row",
        "type": "default",
        "selected": False,
    }
    template.update(edge)
    template["id"] = (
        f"reactflow__edge{edge['source']}{edge['sourceHandle']}-{edge['target']}{edge['targetHandle']}"
    )
    return template


########
# Tools
########
@tool(
    name="get_current_workflow",
    description="Function take no input and return the current workflow in json string format.",
)
def get_current_workflow() -> str:
    return "{}"


@tool(
    name="RAG_search",
    description="Function take a description query and return the top k nodes which are semantically similar to the description. it return the list of dictionary which contains the type and description of the node. The default value of k is 10.",
)
def RAG_search(query: str, k: int = 10) -> list:
    url = "http://*********:8000/embed"

    payload = {"query": query}
    response = requests.post(url, json=payload)
    data = response.json()
    embedding = data["embedding"]
    results = index.query(vector=embedding, top_k=k, include_metadata=True)
    results = results["matches"]
    output = [data["metadata"] for data in results]
    return output


@tool(
    name="get_context",
    description="Function take a item return by the RAG search and as a dictionary not the string and return the context, inputs and output of the node.",
)
def get_context(node_info: dict) -> str:
    return context_helpers[node_info["type"]](node_info)

def post_processing(workflow: dict) -> str:
    output = {}
    output["nodes"] = []
    output["edges"] = []
    k = 0
    node_id_mapping = {}
    for node in workflow["nodes"]:
        if node["OriginalType"] == "StartNode":
            node["node_id"] = "start-node"
        output["nodes"].append(_return_node_template(node))
        node_id_mapping[node["node_id"]] = k
        k += 1
    start_node_id = node_id_mapping["start-node"]
    start_node_config = {"collected_parameters": {}}
    for edge in workflow["edges"]:
        output["edges"].append(_return_edge_template(edge))
        if edge["source"] == "start-node":
            target = edge["target"]
            target_handle = edge["targetHandle"]
            node_parameter = {}
            node_parameter["node_id"] = target
            node_parameter["input_name"] = target_handle
            node_parameter["connected_to_start"] = True
            target_node = output["nodes"][node_id_mapping[target]]
            node_parameter["name_node"] = target_node["data"]["label"]
            target_inputs = target_node["data"]["definition"]["inputs"]
            for input_ in target_inputs:
                if input_["name"] == target_handle:
                    node_parameter["type"] = input_["input_type"]
                    node_parameter["required"] = input_["required"]
                    node_parameter["options"] = input_["options"]
                    break
            start_node_config["collected_parameters"][
                f"{target}_{target_handle}"
            ] = node_parameter
    output["nodes"][start_node_id]["data"]["config"] = start_node_config
    return output


workflow_generation_tools = [RAG_search, get_context]
