{"cells": [{"cell_type": "code", "execution_count": 20, "id": "7d1a8528", "metadata": {}, "outputs": [], "source": ["import requests"]}, {"cell_type": "code", "execution_count": 21, "id": "7799f275", "metadata": {}, "outputs": [], "source": ["url = \"http://127.0.0.1:8000/api/v1/generate_workflow\""]}, {"cell_type": "code", "execution_count": 22, "id": "2b4e72ad", "metadata": {}, "outputs": [], "source": ["short_tasks = [ ## 17\n", "    \"Craft a workflow to create a short video script from a given topic, with a specific duration of one minute. Use this script to generate audio using Eleven Labs with the voice ID TX3LPaxmHKxFdv7VOQHJ, and then fetch the generated audio file.\",\n", "    \"Please generate a workflow that starts with a candidate's resume link. The workflow should then analyze the resume for suitability before sending the analysis results to an API endpoint via a POST request. The API request should have a 10-second timeout.\",\n", "    \"Create a 60-second vertical video about a specific topic, using a male voice for narration. The video should include both stock videos and AI-generated images to complement the script. Make sure the final output includes subtitles synchronized with the audio.\",\n", "    \"Generate a short, inspiring, and academic video for Indian pharmacy students considering a master's degree in the USA. The video should have a voiceover and subtitles. The video should be a combination of stock images and stock video clips.\",\n", "    \"Create a workflow that first extracts all LinkedIn URLs from a given JSON input. The next step should take the extracted URLs and use them to conduct a deep research and profiling report on the associated companies, including funding, leadership, and recent news.\",\n", "    \"Please create a workflow that performs a deep dive on ten LinkedIn profiles. The workflow should execute in parallel, collect the results, and then use an AI agent to perform a comprehensive research and generate a detailed report for each profile. Finally, each report should be appended to a Google Document with the ID 1yIcS8gkT5we0g-EDKSa03hOjDoJnJm-JwB0tRBo7-lo.\",\n", "    \"Please design a workflow that finds a specific row in a Google Sheet. The workflow should start by merging the input data containing a blogId and then use that ID to search for the corresponding row in a Google Sheet named blogs. The search should be limited to the first matching row found in column A.\",\n", "    \"Please create a workflow that automates the process of generating a Google Sheets formula for HR. The workflow should first count the number of rows in a specified Google Sheet, then loop through a range of numbers to generate and apply a unique formula for each row. The formula itself will be generated by an AI agent based on predefined HR criteria and then written to a specified column in the sheet.\",\n", "    \"Create a workflow that retrieves a Jira issue and then generates a comprehensive review of it. The review should then be sent to '<EMAIL>' with the subject 'Issue Review'. The AI agent responsible for this task should only output the review and nothing else.\",\n", "    \"Create a comprehensive blog generation workflow that starts with topic research, generates SEO-optimized content with proper table of contents, creates a hero image, stores everything in Redis, converts content to HTML format, publishes to Google Docs, tracks metadata in Google Sheets, and finally cleans up temporary data from Redis. The workflow should handle content research, TOC generation, section-by-section content creation, image generation, HTML conversion, and document publishing with proper metadata management.\",\n", "    \"Design a workflow that begins by merging an initial input with a spreadsheet input, followed by a component that selects the 'range' key from the merged data. Next, use the selected range to retrieve data from a Google Sheet with the spreadsheet ID \\\"1eeuNf_4fzd3f58RSR73mkHpsH2ypWBnT9KaVaHtZu_s\\\". Finally, select the 'values' from the Google Sheet output and convert them into an array format.\",\n", "    \"Generate a workflow to review a Jira issue and send a review email. The workflow should get the issue details using a Jira tool, use an agent to create a comprehensive review of the issue, and finally, send an email with the review as the <NAME_EMAIL> with the subject \\\"Issue Review\\\".\",\n", "    \"Please create a workflow that starts by searching for a specific value in a Google Sheet. The workflow should find the first matching row and return it. The search should be conducted in column A of the worksheet named \\\"JD\\\" within the spreadsheet with the ID \\\"1INySvJYwS7ZzC2CYbAmKuDmNUc4stXbec5lcWUnybJ4.\\\"\",\n", "    \"Make a workflow that starts and then adds a row into my Google Sheet.\",\n", "    \"Create a workflow that finds a specific row in a Google Sheet and then updates it. The workflow should start by searching for a value in column 'A' of the 'company_details' worksheet within the spreadsheet '1INySvJYwS7ZzC2CYbAmKuDmNUc4stXbec5lcWUnybJ4'. Then, it should use the row number of the found match to update that specific row.\",\n", "    \"Craft a workflow to retrieve job descriptions from a Google Sheet. The workflow should get all data from the sheet named JD within the spreadsheet ID 1vGJDuTKZE0nawv40HFGCJt6EWBPKMnBA53hM8VOYF2Y and then use an Agentic AI to format the retrieved data. The final output must be in a mobile-friendly, card-based layout with semicolon-separated list items converted to bullet points, separated by horizontal rules, and without any additional introductory or concluding text.\",\n", "    \"Build a simple workflow that connects a start node to a Gmail node, which should create a new email draft with the subject line \\\"Test mail\\\" and a message body of \\\"Hello.\\\"\",\n", "]\n", "\n", "long_task = [ ## 30\n", "    \"Create a workflow that searches for the top 10 results on a given topic using an AI agent to generate summaries. It processes and aggregates these summaries, generates related hero images, and merges all content and metadata.Then, it builds a detailed table of contents and uses AI to produce a structured, formatted blog post in HTML with valid image embeds. The workflow outputs a complete JSON response ready for publishing in Webflow CMS, including metadata and content.\",\n", "    \"Create a cinematic video generation pipeline. The pipeline should start by generating a script, then iterate through each scene to generate a corresponding image and video. Finally, it should compile the generated videos, audio, and subtitles into a complete cinematic video.\",\n", "    \"Create a workflow that starts by generating structured marketing content for a pitch deck using a search tool or URL fetch, then selects an appropriate presentation template based on the content's tone and domain. Next, format the content into slide-ready JSON with specified slide counts and vivid descriptions, ensuring logical segmentation and persuasive language. Finally, generate a PowerPoint presentation slide-by-slide using the formatted JSON and selected template, returning a downloadable presentation URL.\",\n", "    \"Create a workflow that searches for top AI agent-related blog content, summarizes and categorizes it, generates a cohesive blog post with a table of contents, and produces a hero image and HTML for Webflow CMS. The process involves iterative data processing, Redis storage, and structured JSON output for seamless integration.\",\n", "    \"Create a workflow that starts with a Context-Creator agent analyzing a user query to generate a marketing-aligned website context, followed by a Code-Setup agent initializing a React Vite repository using the repo_setup tool. Next, have a Layout-Definer agent design a hero section based on the context, followed by a Create-File agent generating responsive React tsx code with Tailwind CSS for the hero section, utilizing tavily-extract, list_files, and read_file tools to ensure compatibility. Finally, include a Code-Pusher agent to commit and push changes to the repository using the push_changes tool, ensuring seamless updates.\",\n", "    \"Create a workflow that retrieves data from a Google Sheets document to find a venture capital investor marked as \\\"researched,\\\" extracts their name and background information, and combines it with company context to generate two personalized, humanized outreach emails using an AI model. The workflow should update specific cells in the Google Sheet with the generated emails and their humanized versions, ensuring the emails are conversational, avoid promotional triggers, and align with Gmail's Primary inbox criteria. Finally, merge the email data, update the status cell to \\\"email_generated,\\\" and store all results back in the Google Sheet for tracking.\",\n", "    \"Create a workflow that generates a professional job description for a Senior Full Stack Engineer using provided job data, fetches company details from Google Sheets, and formats the output as a structured JSON object. The process should store the job description in a new Google Doc, update the spreadsheet with the document URL, and add the job data to a designated worksheet. Ensure the workflow uses Markdown formatting for the document, incorporates a company logo, and maintains SEO-friendly, concise content tailored to the company\\u2019s culture and industry.\",\n", "    \"Create a workflow that generates a Google Doc from a given content input, sets its sharing permissions, and then uses the generated document's details to compose and send an email to a specified recipient. The workflow should ensure that the email is sent directly, containing the document's title, a brief summary, and the actual link. The first AI agent should create and format the document, and the second AI agent should handle the email composition and delivery.\",\n", "    \"Create a Meta Ads campaign with a daily budget of $1000 to promote a solar power bank. The campaign should have a traffic objective and be configured for a single ad set that targets India on Facebook's feed. Upload an image, then use it to create an ad creative with a link and a call-to-action before creating the final ad.\",\n", "    \"Create a detailed research workflow that starts by gathering comprehensive company information, then extracts the LinkedIn URLs of key executives and the company, and finally uses that data to conduct further research on both the company and its employees.\",\n", "    \"Build a workflow to process an investment thesis document. The initial step should be to run the document through a 'Investment Thesis Parser' AI agent, which formats its output as a specific JSON object. The second step should be a document converter that takes the JSON output and converts it to an object. The final step should be to use a 'Data Extractor' agent to extract only the company's name from this object, which will then be passed to a 'Research Workflow' component.\",\n", "    \"Create a workflow that searches for top chocolate-related blog content, generates an SEO-optimized table of contents, produces detailed content, converts it to HTML, creates a hero image, and stores metadata in Redis for Webflow CMS integration. The process ensures structured JSON output with comprehensive metadata and formatted content for seamless blog post creation.\",\n", "    \"Create a workflow that automates the research of a venture capital investor. Start by searching a Google Sheet for an investor's email to find their name, firm, and LinkedIn URL. Then, use this information to conduct four parallel research tasks, gathering data on their recent investments, funding rounds, portfolio companies, and personal profile. Finally, merge all the research findings and update the Google Sheet with the new data.\",\n", "    \"Create a comprehensive workflow for automated blog post generation starting from a user-provided topic, writing style, max words, and audience, where the process begins with researching top sources via an AI agent, generates an SEO-optimized table of contents (TOC), and loops through each TOC section to produce content using specialized AI agents for writing, formatting, and HTML conversion. Incorporate Redis for temporary storage of research summaries, TOC, content, and hero images generated via Leonardo AI based on TOC-derived prompts, while merging and selecting data at key points to ensure seamless integration. Conclude the workflow by generating Webflow-compatible metadata fields, retrieving all components from Redis, merging them into a final structure, and publishing the complete blog post to a specified Webflow CMS collection using the appropriate API component.\",\n", "    \"Create a workflow that starts with a trigger and uses an AI agent to generate a blog research summary on a specified topic. The workflow converts data formats, generates an SEO-optimized table of contents, and produces structured blog content. It generates related hero images and stores intermediate results in Redis.The workflow processes the data in loops, merges all generated content and metadata, and creates a Webflow blog post with the final data. It handles errors and parallel execution efficiently while ensuring all content transformations and storages occur seamlessly.\",\n", "    \"Please generate a workflow that gets responses from a Google Form, loops through each response, and checks if a row with the corresponding response ID already exists in a Google Sheet. The workflow should convert the Google Form responses to objects and select the \\\"responses\\\" key for the loop, and then select the \\\"responseId\\\" key inside the loop to search for a matching row in column \\\"A\\\" of the Google Sheet. Finally, it should check if the search result for the rows is an empty list and merge the Google Form response data with the output of the conditional check.\",\n", "    \"Please create a workflow that automates the candidate screening process for a UI Designer role. The workflow should start by fetching a list of candidates from a Google Sheet, filtering out those that are \\\"NOT SELECTED\\\" or \\\"REVIEWED,\\\" and then looping through the remaining candidates. For each candidate, the workflow should download their resume, extract the text, and merge it with the job description from another Google Sheet before sending both to an AI agent for a relevance score and summary. The final steps should update the original Google Sheet with the ATS score, summary, and a \\\"REVIEWED\\\" status for each candidate.\",\n", "    \"Create a workflow that starts with a trigger to search and analyze the top 10 popular results for a given topic using an AI agent, generating detailed and extensive summaries. It converts and processes these summaries in a loop, merges them, and generates hero images.The workflow stores intermediate data in Redis, merges all content and metadata, and builds an SEO-optimized table of contents. It then uses an AI content generator to create a structured, detailed blog post in HTML with proper headings, paragraphs, and bullet lists.Finally, it outputs a complete JSON response suitable for Webflow CMS, including metadata such as title, slug, excerpt, tags, publish date, featured image, and fully formatted blog content ready for publishing.\",\n", "    \"Build a workflow that automatically generates a blog post for Webflow CMS, starting from a topic and writing style input. The workflow should research the topic with AI, extract structured summaries, generate a table of contents, and iterate through each TOC section to create SEO-optimized content. It will also prepare formatted HTML, a prompt for a hero image, store all data in Redis (including TOC, content, and featured image), and finally compile complete blog fields and metadata. After publishing the blog post to Webflow, the workflow removes all related hashes and summary keys from Redis to clean up temporary storage.\",\n", "    \"Build a workflow that takes a blogId and operationType as input. The workflow should then use a Switch-Case Router to either find a specific row in a Google Sheet based on the blogId or retrieve a range of values, depending on whether the operationType is 'row' or something else. Finally, the selected data is passed to the appropriate Google Sheets action to complete the process.\",\n", "    \"Please create a workflow that retrieves data from a Google Sheet. The workflow should start by receiving a user's input, which includes the specific range to pull from, and then pass that information to the Google Sheets node to fetch the values. Finally, the fetched data should be converted into an object format.\",\n", "    \"Create a workflow that starts with a trigger to generate a blog research summary on a given topic using an AI agent. It produces an SEO-optimized table of contents and structured blog content, converts formats as needed, and generates related hero images. The workflow caches intermediate data in Redis, processes content in loops, and merges all data and metadata.Finally, it creates and appends Google Documents for the blog and inserts the combined content into a Google Sheet, handling errors and parallel execution efficiently throughout the process.\",\n", "    \"Create a workflow that generates a blog post by researching a given topic and writing style, then structures the output into an SEO-optimized format for Webflow CMS. The workflow extracts and converts content, splits it into sections via a table of contents, and uses AI to generate detailed content for each section while preserving numbering only if present. It generates a hero image prompt and saves all intermediary data such as TOC, content, and images in Redis. After compiling the full blog post data, it invokes a secondary workflow to create a Google Document with the blog content. The process includes merging metadata and content, cleaning up temporary Redis keys, and managing content serialization. The secondary workflow handles final document creation and publishing.\",\n", "    \"Create a workflow that takes a blog topic and preferences as input, researches and summarizes top sources, generates an SEO-optimized table of contents, and produces detailed blog sections. The content is converted to HTML, a banner image prompt is created, and metadata is generated and stored in Redis. The blog content is assembled into a Google Document, a summary row is added to Google Sheets, and temporary data is cleaned up.\",\n", "    \"Create an automation that finds a specific job ID in a Google Sheet named \\\"JD\\\". The workflow should then update the status of that job in column \\\"M\\\" to \\\"Rejected\\\" based on the row number found. Finally, the automation should use an AI agent to generate and send a rejection email to the candidate, including a detailed reason for their rejection, with all the necessary information sourced from the Google Sheet.\",\n", "    \"Create a workflow that generates a professional job description for a Senior React Developer with 5+ years of experience, using company details retrieved from a specified Google Doc, and formats the output as a structured job description in Markdown. The workflow should then store the job description data in a Google Sheets worksheet named \\\"JobDescriptions,\\\" generate a unique job ID, and update the spreadsheet with a link to a newly created Google Doc containing the formatted job description. Ensure the process includes data conversion, row insertion, and document creation with proper integration of Google Sheets and Docs APIs, maintaining a clear flow from input to final document URL storage.\",\n", "    \"Build a workflow that starts by processing user input on a blog topic, writing style, maximum word count, and audience. It then uses an AI agent to research and find the top 10 relevant sources for the topic, generating both concise and extensive summaries. This data is converted into a standard JSON format, followed by generating an SEO-optimized table of contents for the blog. The workflow loops through each section of the TOC to generate high-quality, SEO-optimized blog content with professional tone, structure, and formatting. Afterward, it converts the content into HTML format and generates a banner image prompt based on the content. The blog metadata including summaries, tags, and descriptions are generated and stored in Redis. The workflow also manages retrieval and updating of data from Redis throughout the process. Finally, the completed content sections are appended to a new Google Document. The workflow concludes by adding a new row with relevant data into a Google Sheet for record keeping and cleans up temporary storage keys.\",\n", "    \"Create a workflow that generates and applies a Google Sheets formula to multiple rows. The process should begin by getting the total number of entries in Column 'A' of a specified spreadsheet. This count will define the range of a loop. Inside the loop, an AI agent should generate a Google Sheets formula based on given constraints: min_notice_prd, min_exp_req, min_ctc_req, and max_ectc_req. The agent's output should be a JSON object with the formula and a size parameter. This formula should then be applied to cells in Column 'M' for each row within the determined range.\",\n", "    \"Please create a B2B sales automation workflow that gets a campaign's details, generates a list of target companies and personas, and then enriches the data for those customers. Once enriched, the workflow should then create customer profiles in our SDR management system and finally, create new customer profiles in the same SDR system.\",\n", "    \"Please generate a workflow that automates the process of replying to a customer's email. The workflow should first fetch the email's content and related information, then use that data to conduct a comprehensive analysis to identify the customer's profile, pain points, and how our products align with their needs. Finally, the workflow should draft a personalized, professional email response and send it to the customer.\",\n", "]"]}, {"cell_type": "code", "execution_count": 23, "id": "01d1107b", "metadata": {}, "outputs": [], "source": ["import time\n", "output = []"]}, {"cell_type": "code", "execution_count": 24, "id": "cb64c3c0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Task 0 done in 187.43727469444275 seconds\n"]}], "source": ["i = 0\n", "task = long_task[i]\n", "start = time.time()\n", "try:\n", "    response = requests.post(url, json={\"task\": task})\n", "    end = time.time()\n", "    output.append({\"task_id\":i,\"task\": task, \"response\": response.content, \"time\": end - start})\n", "except Exception as e:\n", "    end = time.time()\n", "    print(f\"Error for task {i}: {e}\")\n", "print(f\"Task {i} done in {end - start} seconds\")"]}, {"cell_type": "code", "execution_count": 26, "id": "d1de2a3f", "metadata": {}, "outputs": [], "source": ["from helper import safe_loads\n", "\n", "workflow = safe_loads(output[0][\"response\"])"]}, {"cell_type": "code", "execution_count": 30, "id": "8d71c787", "metadata": {}, "outputs": [{"data": {"text/plain": ["9"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["len(workflow[\"workflow\"][\"nodes\"])"]}, {"cell_type": "code", "execution_count": 28, "id": "b1ffef20", "metadata": {}, "outputs": [{"data": {"text/plain": ["11"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["len(workflow[\"workflow\"][\"edges\"])"]}, {"cell_type": "code", "execution_count": 29, "id": "74a4aae1", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["len(output)"]}, {"cell_type": "code", "execution_count": 1, "id": "85ab1dca", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/lib/python3.13/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "Graph without execution limits may run indefinitely if cycles exist\n"]}], "source": ["import uuid\n", "\n", "from agent import get_agents\n", "from graph import get_graph\n", "from logger_config import get_logger\n", "\n", "session_id = str(uuid.uuid4())\n", "logger = get_logger(session_id)\n", "agents = get_agents(logger)\n", "graph = get_graph(agents)\n", "\n", "task = \"Craft a workflow to create a short video script from a given topic, with a specific duration of one minute. Use this script to generate audio using Eleven Labs with the voice ID TX3LPaxmHKxFdv7VOQHJ, and then fetch the generated audio file.\"\n", "\n", "result = graph(task)"]}, {"cell_type": "code", "execution_count": null, "id": "3225a2e5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}